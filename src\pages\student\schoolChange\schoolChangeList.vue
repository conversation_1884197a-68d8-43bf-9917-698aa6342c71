<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的学籍异动',
  },
}
</route>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getSchoolChangeApply, getStudentInfo } from '@/service/student'
import { useUserStore } from '@/store/user'
import ActionButton from '@/components/common/ActionButton.vue'
import type {
  SchoolChangeApplyQuery,
  SchoolChangeApplyResponse,
  SchoolChangeApplyItem,
  StudentInfo,
} from '@/types/student'

/** 加载状态 */
const loading = ref(false)

/** 用户store */
const userStore = useUserStore()

/** 学生信息 */
const studentInfo = ref({
  name: userStore.userInfo.realname || '',
  className: userStore.userInfo.className || '',
  studentId: userStore.userInfo.username || '',
  avatarUrl: userStore.userInfo.avatar,
  status: '在籍在读',
  enterDate: '',
  changeCount: '0次',
  graduateDate: '',
})

/** 获取学生信息 */
const fetchStudentInfo = async () => {
  // 基本信息已从userStore获取，仅获取额外信息
  if (!studentInfo.value.enterDate || !studentInfo.value.graduateDate) {
    try {
      const res = await getStudentInfo()
      studentInfo.value = {
        ...studentInfo.value,
        status: res.stuSchoolStatus || '在籍在读',
        enterDate: res.startStudyDate || '',
        // 处理毕业时间为"0000-00-00"的情况
        graduateDate: res.bysj === '0000-00-00' ? '' : res.bysj || '',
      }
    } catch (error) {
      console.error('获取学生信息失败', error)
    }
  }
}

/** 分段控制器选中索引 */
const activeSegmentIndex = ref(0)

/** 分段控制器列表 */
const segments = ['全部', '审批中', '已完成', '已拒绝']

/** 切换分段 */
const handleSegmentChange = (index: number) => {
  activeSegmentIndex.value = index
  fetchSchoolChangeList()
}

/** 查询参数 */
const queryParams = ref<SchoolChangeApplyQuery>({
  page: 1,
  pageSize: 10,
  semesters: [],
  id: '',
  changeTypeName: '',
  changeReasonName: '',
  changeReasonDetails: '',
  applicationTime: '',
  counselorApproval: '',
  deptApproval: '',
  academicAffairsApproval: '',
})

/** 根据分段控制器索引设置查询参数 */
const setQueryParamsBySegment = () => {
  // 重置筛选条件
  queryParams.value.counselorApproval = ''
  queryParams.value.deptApproval = ''
  queryParams.value.academicAffairsApproval = ''

  // 根据选中的分段设置筛选条件
  switch (activeSegmentIndex.value) {
    case 1: // 审批中
      queryParams.value.counselorApproval = '0' // 0-待审批
      break
    case 2: // 已完成
      queryParams.value.academicAffairsApproval = '1' // 1-通过
      break
    case 3: // 已拒绝
      queryParams.value.academicAffairsApproval = '-1' // -1-拒绝
      break
    default:
      // 全部，不设置筛选条件
      break
  }
}

/** 学籍异动列表响应数据 */
const schoolChangeResponse = ref<SchoolChangeApplyResponse>({
  items: [],
  query: {},
  total: 0,
})

/** 学籍异动列表数据 */
const schoolChangeList = computed(() => {
  return schoolChangeResponse.value.items || []
})

/** 获取学籍异动列表 */
const fetchSchoolChangeList = async () => {
  loading.value = true
  try {
    setQueryParamsBySegment()
    const res = await getSchoolChangeApply(queryParams.value)
    schoolChangeResponse.value = res

    // 更新变更次数
    if (res.total !== undefined) {
      studentInfo.value.changeCount = `${res.total}次`
    }
  } catch (error) {
    console.error('获取学籍异动列表失败', error)
  } finally {
    loading.value = false
  }
}

/** 根据审批状态获取状态文本和颜色类名 */
const getStatusInfo = (item: SchoolChangeApplyItem) => {
  let status = 'pending'
  let statusText = '审核中'

  // 判断审批状态 (0-待审批, 1-通过, 2-驳回, -1-拒绝)
  if (item.academicAffairsApproval === 1) {
    status = 'approved'
    statusText = '已通过'
  } else if (
    item.academicAffairsApproval === 2 ||
    item.academicAffairsApproval === -1 ||
    item.counselorApproval === 2 ||
    item.counselorApproval === -1 ||
    item.deptApproval === 2 ||
    item.deptApproval === -1 ||
    item.deptLeaderApproval === 2 ||
    item.deptLeaderApproval === -1
  ) {
    // 任何一个环节被拒绝，整个申请都视为已拒绝
    status = 'rejected'
    statusText = '已拒绝'
  } else if (
    item.counselorApproval === 0 ||
    item.deptApproval === 0 ||
    item.deptLeaderApproval === 0 ||
    item.academicAffairsApproval === 0
  ) {
    status = 'pending'
    statusText = '审核中'
  }

  return { status, statusText }
}

/** 格式化时间线数据 */
const formatTimeline = (item: SchoolChangeApplyItem) => {
  const timeline = [
    {
      date: item.applicationTime || '',
      title: '提交申请',
      desc: `已提交${item.changeTypeName}申请，等待辅导员审批`,
      status: 'completed',
    },
  ]

  // 辅导员审批
  if (item.counselorApproval !== undefined) {
    // 0-待审批, 1-通过, 2-驳回, -1-拒绝
    const status =
      item.counselorApproval === 1
        ? 'completed'
        : item.counselorApproval === 2 || item.counselorApproval === -1
          ? 'rejected'
          : 'pending'

    // 获取审批状态文本
    const approvalText =
      item.counselorApproval === 1
        ? '通过'
        : item.counselorApproval === 2
          ? '驳回'
          : item.counselorApproval === -1
            ? '拒绝'
            : '审核中'

    let date = ''
    if (item.update_time) {
      // 只有当时间戳有效时才转换日期
      const timestamp = Number(item.update_time)
      if (!isNaN(timestamp) && timestamp > 946684800000) {
        // 2000-01-01 的时间戳，用于过滤无效日期
        date = new Date(timestamp).toLocaleString()
      }
    }

    timeline.push({
      date,
      title: '辅导员审批',
      desc: `辅导员${approvalText}${item.counselorApproval === 1 ? '，等待系部审批' : ''}`,
      status,
    })
  }

  // 系部审批（仅当辅导员通过时才显示）
  if (item.counselorApproval === 1) {
    // 0-待审批, 1-通过, 2-驳回, -1-拒绝
    const status =
      item.deptApproval === 1
        ? 'completed'
        : item.deptApproval === 2 || item.deptApproval === -1
          ? 'rejected'
          : 'pending'

    // 获取审批状态文本
    const approvalText =
      item.deptApproval === 1
        ? '通过'
        : item.deptApproval === 2
          ? '驳回'
          : item.deptApproval === -1
            ? '拒绝'
            : '审核中'

    let date = ''
    if (item.update_time) {
      const timestamp = Number(item.update_time)
      if (!isNaN(timestamp) && timestamp > 946684800000) {
        date = new Date(timestamp).toLocaleString()
      }
    }

    timeline.push({
      date,
      title: '系部审批',
      desc:
        item.deptApproval !== undefined
          ? `系部${approvalText}${item.deptApproval === 1 ? '，等待教务处审批' : ''}`
          : '系部审批中...',
      status,
    })
  }

  // 教务处审批（仅当系部通过时才显示）
  if (item.deptApproval === 1) {
    // 0-待审批, 1-通过, 2-驳回, -1-拒绝
    const status =
      item.academicAffairsApproval === 1
        ? 'completed'
        : item.academicAffairsApproval === 2 || item.academicAffairsApproval === -1
          ? 'rejected'
          : 'pending'

    // 获取审批状态文本
    const approvalText =
      item.academicAffairsApproval === 1
        ? '通过'
        : item.academicAffairsApproval === 2
          ? '驳回'
          : item.academicAffairsApproval === -1
            ? '拒绝'
            : '审核中'

    let date = ''
    if (item.update_time) {
      const timestamp = Number(item.update_time)
      if (!isNaN(timestamp) && timestamp > 946684800000) {
        date = new Date(timestamp).toLocaleString()
      }
    }

    timeline.push({
      date,
      title: '教务处审批',
      desc: item.academicAffairsApproval !== undefined ? `教务处${approvalText}` : '等待教务处审批',
      status,
    })
  }

  return timeline
}

/** 查看详情 */
const viewDetail = (id: number) => {
  console.log('查看详情', id)
}

/** 下载审批表 */
const downloadApproval = (id: number) => {
  console.log('下载审批表', id)
}

/** 撤销申请 */
const cancelApplication = (id: number) => {
  console.log('撤销申请', id)
}

/** 创建新申请 */
const createApplication = () => {
  uni.navigateTo({
    url: '/pages/student/schoolChange/schoolChangeApply',
  })
}

/** 页面加载时获取数据 */
onMounted(() => {
  fetchStudentInfo()
  fetchSchoolChangeList()
})
</script>

<template>
  <!-- 分段控制器 -->

  <!-- 内容区域 -->
  <view class="content-container px-4 py-4 bg-[#f2f2f7]">
    <!-- 学籍信息 -->
    <view class="student-info-card bg-white rounded-lg p-4 shadow-sm mb-4">
      <view class="flex items-center mb-4">
        <image :src="studentInfo.avatarUrl" class="mr-4 h-16 w-16 rounded-full" mode="aspectFill" />
        <view>
          <view class="text-lg font-semibold">{{ studentInfo.name }}</view>
          <view class="text-sm text-gray-500">{{ studentInfo.className }}</view>
          <view class="text-sm text-gray-500">学号: {{ studentInfo.studentId }}</view>
        </view>
      </view>
      <view class="grid grid-cols-2 gap-2">
        <view class="text-sm">
          <text class="text-gray-500">学籍状态：</text>
          <text class="text-green-500">{{ studentInfo.status }}</text>
        </view>
        <view class="text-sm">
          <text class="text-gray-500">入学时间：</text>
          <text>{{ studentInfo.enterDate }}</text>
        </view>
        <view class="text-sm">
          <text class="text-gray-500">学籍变更：</text>
          <text>{{ studentInfo.changeCount }}</text>
        </view>
        <view class="text-sm">
          <text class="text-gray-500">预计毕业：</text>
          <text>{{ studentInfo.graduateDate }}</text>
        </view>
      </view>
    </view>

    <!-- 申请按钮 -->
    <view class="apply-button-container bg-white rounded-lg p-2 shadow-sm mb-[32rpx]">
      <ActionButton type="primary" text="学期异动申请" @click="createApplication" />
    </view>

    <!--  <view class="segment-container bg-white flex rounded-lg p-1 my-4">
      <view
        v-for="(segment, index) in segments"
        :key="index"
        class="segment-item flex-1 py-2 text-center text-sm transition-all"
        :class="[activeSegmentIndex === index ? 'segment-active' : '']"
        @click="handleSegmentChange(index)"
      >
        {{ segment }}
      </view>
    </view> -->

    <!-- 加载状态 -->
    <view v-if="loading" class="flex justify-center py-12">
      <view class="loading-icon">
        <wd-icon name="refresh" custom-style="font-size: 24px; color: var(--primary-color);" />
      </view>
    </view>

    <!-- 无数据提示 -->
    <view
      v-else-if="schoolChangeList.length === 0"
      class="flex flex-col items-center justify-center py-12"
    >
      <wd-icon name="note" custom-style="font-size: 48px; color: #ccc; margin-bottom: 16px;" />
      <view class="text-gray-500">暂无学籍异动记录</view>
    </view>

    <!-- 学籍异动卡片 -->
    <view
      v-else
      v-for="item in schoolChangeList"
      :key="item.id"
      class="status-card bg-white mb-4 overflow-hidden rounded-lg shadow-sm"
    >
      <view class="status-header flex items-center justify-between border-b border-gray-100 p-4">
        <view class="text-base font-semibold">{{ item.changeTypeName }}</view>
        <view
          class="status-tag rounded px-2 py-1 text-xs text-white"
          :class="{
            'bg-orange-500': getStatusInfo(item).status === 'pending',
            'bg-green-500': getStatusInfo(item).status === 'approved',
            'bg-red-500': getStatusInfo(item).status === 'rejected',
            'bg-blue-500': getStatusInfo(item).status === 'completed',
          }"
        >
          {{ getStatusInfo(item).statusText }}
        </view>
      </view>

      <view class="status-content p-4">
        <!-- 基本信息 -->
        <view class="mb-4">
          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">申请类型:</view>
            <view class="flex-1 text-sm">{{ item.changeTypeName }}</view>
          </view>

          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">申请原因:</view>
            <view class="flex-1 text-sm">{{ item.changeReasonName }}</view>
          </view>

          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">详细说明:</view>
            <view class="flex-1 text-sm">{{ item.changeReasonDetails || '无' }}</view>
          </view>

          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">申请时间:</view>
            <view class="flex-1 text-sm">{{ item.applicationTime }}</view>
          </view>

          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">申请描述:</view>
            <view class="flex-1 text-sm">{{ item.applicationDescription || '无' }}</view>
          </view>

          <view class="mb-2 flex">
            <view class="w-20 text-sm text-gray-500">学年学期:</view>
            <view class="flex-1 text-sm">{{ item.studyYear }}学年 第{{ item.studyTerm }}学期</view>
          </view>
        </view>

        <!-- 时间线 -->
        <view class="timeline relative pl-8 mt-5">
          <view
            v-for="(timeline, timelineIndex) in formatTimeline(item)"
            :key="timelineIndex"
            class="timeline-item relative mb-5 last:mb-0"
          >
            <view
              class="timeline-dot absolute left-[-30px] top-0 flex h-5 w-5 items-center justify-center rounded-full text-white"
              :class="{
                'bg-green-500': timeline.status === 'completed',
                'bg-orange-500': timeline.status === 'pending',
                'bg-red-500': timeline.status === 'rejected',
              }"
            >
              <wd-icon
                v-if="timeline.status === 'completed'"
                name="check"
                custom-style="font-size: 12px;"
              />
              <wd-icon
                v-else-if="timeline.status === 'pending'"
                name="time"
                custom-style="font-size: 12px;"
              />
              <wd-icon
                v-else-if="timeline.status === 'rejected'"
                name="close"
                custom-style="font-size: 12px;"
              />
            </view>
            <view class="timeline-content rounded-lg bg-gray-50 p-3 shadow-sm">
              <view class="timeline-date mb-1 text-xs text-gray-500">{{ timeline.date }}</view>
              <view class="timeline-title mb-1 text-sm font-semibold">{{ timeline.title }}</view>
              <view class="timeline-desc text-xs text-gray-500">{{ timeline.desc }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <!-- <view class="status-actions flex justify-end border-t border-gray-100 p-3">
        <view
          v-if="getStatusInfo(item).status === 'pending'"
          class="status-button"
          @click="cancelApplication(item.id)"
        >
          <wd-icon
            name="delete-thin"
            custom-style="margin-right: 4px; font-size: 14px; color: #f44336;"
          />
          <text class="text-sm text-red-500">撤销申请</text>
        </view>
        <view
          v-if="getStatusInfo(item).status === 'approved'"
          class="status-button ml-4"
          @click="downloadApproval(item.id)"
        >
          <wd-icon name="download" custom-style="margin-right: 4px; font-size: 14px;" />
          <text class="text-sm text-primary">下载审批表</text>
        </view>
        <view class="status-button ml-4" @click="viewDetail(item.id)">
          <wd-icon name="view" custom-style="margin-right: 4px; font-size: 14px;" />
          <text class="text-sm text-primary">查看详情</text>
        </view>
      </view> -->
    </view>
  </view>
</template>

<style scoped>
.loading-icon {
  animation: spin 1.5s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

<style lang="scss">
.segment-container {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.segment-item {
  cursor: pointer;
}

.segment-active {
  font-weight: 500;
  color: var(--primary-color, #0083ff);
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.status-button {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.timeline {
  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 4px;
    width: 2px;
    content: '';
    background-color: #e0e0e0;
  }
}

.add-button {
  z-index: 10;
  background: var(--primary-color, #0083ff);
}
</style>
